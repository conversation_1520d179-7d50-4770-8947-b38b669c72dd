/*!
 * Modern UI Enhancements for AdminLTE
 * Custom CSS overrides for contemporary design
 * Author: Custom Enhancement
 * Version: 1.0.0
 */

/* ===== CSS VARIABLES FOR CONSISTENT THEMING ===== */
:root {
  /* Modern Color Palette */
  --primary-color: #4f46e5;
  --primary-hover: #4338ca;
  --primary-light: #e0e7ff;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --info-color: #06b6d4;

  /* Light Mode Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --text-primary: #1e293b;
  --text-secondary: #475569;
  --text-muted: #64748b;
  --border-color: #e2e8f0;
  --border-light: #f1f5f9;
  --shadow-color: rgba(0, 0, 0, 0.1);

  /* Neutral Colors */
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;
  
  /* Typography */
  --font-family-sans: 'Inter', 'Source Sans Pro', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* ===== DARK MODE VARIABLES ===== */
[data-theme="dark"] {
  /* Dark Mode Color Overrides */
  --bg-primary: #1e293b;
  --bg-secondary: #0f172a;
  --bg-tertiary: #334155;
  --text-primary: #f1f5f9;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --border-color: #475569;
  --border-light: #334155;
  --shadow-color: rgba(0, 0, 0, 0.3);

  /* Dark Mode Specific Colors */
  --primary-light: #312e81;
  --gray-50: #0f172a;
  --gray-100: #1e293b;
  --gray-200: #334155;
  --gray-300: #475569;
  --gray-400: #64748b;
  --gray-500: #94a3b8;
  --gray-600: #cbd5e1;
  --gray-700: #e2e8f0;
  --gray-800: #f1f5f9;
  --gray-900: #f8fafc;
}

/* ===== THEME TOGGLE BUTTON ===== */
.theme-toggle {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1050;
  background: var(--bg-primary);
  border: 2px solid var(--border-color);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: 0 4px 12px var(--shadow-color);
  color: var(--text-primary);
}

.theme-toggle:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px var(--shadow-color);
}

.theme-toggle i {
  font-size: 1.2rem;
  transition: all var(--transition-fast);
}

/* Hide/show icons based on theme */
[data-theme="light"] .theme-toggle .fa-moon {
  display: block;
}

[data-theme="light"] .theme-toggle .fa-sun {
  display: none;
}

[data-theme="dark"] .theme-toggle .fa-moon {
  display: none;
}

[data-theme="dark"] .theme-toggle .fa-sun {
  display: block;
}

/* ===== GLOBAL IMPROVEMENTS ===== */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-family-sans) !important;
  font-weight: var(--font-weight-normal);
  line-height: 1.6;
  color: var(--text-primary) !important;
  background-color: var(--bg-secondary) !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color var(--transition-normal), color var(--transition-normal);
}

.wrapper, .content-wrapper {
  background-color: var(--bg-secondary) !important;
  transition: background-color var(--transition-normal);
}

/* ===== ENHANCED TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-sans) !important;
  font-weight: var(--font-weight-semibold) !important;
  color: var(--text-primary) !important;
  line-height: 1.4;
  margin-bottom: var(--spacing-md);
  transition: color var(--transition-normal);
}

h1 { font-size: 2.25rem; }
h2 { font-size: 1.875rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
  margin-bottom: var(--spacing-md);
  color: var(--text-secondary);
  transition: color var(--transition-normal);
}

/* ===== ENHANCED NAVIGATION ===== */
.main-header.navbar {
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-tertiary) 100%) !important;
  border-bottom: 1px solid var(--border-color) !important;
  box-shadow: var(--shadow-sm) !important;
  backdrop-filter: blur(10px);
  transition: all var(--transition-normal);
}

.navbar-brand {
  transition: all var(--transition-fast);
}

.navbar-brand:hover {
  transform: translateY(-1px);
}

.brand-text {
  font-weight: var(--font-weight-bold) !important;
  color: var(--primary-color) !important;
  font-size: 1.5rem !important;
}

/* ===== MODERN CARD STYLING ===== */
.card {
  border: 1px solid var(--border-color) !important;
  border-radius: var(--radius-xl) !important;
  box-shadow: 0 4px 6px -1px var(--shadow-color), 0 2px 4px -1px var(--shadow-color) !important;
  background: var(--bg-primary) !important;
  transition: all var(--transition-normal);
  overflow: hidden;
  margin-bottom: var(--spacing-xl) !important;
}

.card:hover {
  box-shadow: 0 10px 15px -3px var(--shadow-color), 0 4px 6px -2px var(--shadow-color) !important;
  transform: translateY(-2px);
}

.card-header {
  background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-primary) 100%) !important;
  border-bottom: 1px solid var(--border-color) !important;
  padding: var(--spacing-lg) var(--spacing-xl) !important;
  border-top-left-radius: var(--radius-xl) !important;
  border-top-right-radius: var(--radius-xl) !important;
}

.card-title {
  font-size: 1.25rem !important;
  font-weight: var(--font-weight-semibold) !important;
  color: var(--text-primary) !important;
  margin: 0 !important;
  transition: color var(--transition-normal);
}

.card-body {
  padding: var(--spacing-xl) !important;
}

.card-footer {
  background: var(--bg-tertiary) !important;
  border-top: 1px solid var(--border-color) !important;
  padding: var(--spacing-lg) var(--spacing-xl) !important;
  border-bottom-left-radius: var(--radius-xl) !important;
  border-bottom-right-radius: var(--radius-xl) !important;
}

/* Card Variants */
.card-primary {
  border-color: var(--primary-color) !important;
}

.card-primary .card-header {
  background: linear-gradient(135deg, var(--primary-light) 0%, #ffffff 100%) !important;
  border-bottom-color: var(--primary-color) !important;
}

.card-outline {
  border-width: 2px !important;
}

/* ===== ENHANCED BUTTONS ===== */
.btn {
  font-family: var(--font-family-sans) !important;
  font-weight: var(--font-weight-medium) !important;
  border-radius: var(--radius-md) !important;
  padding: 0.625rem 1.25rem !important;
  font-size: 0.875rem !important;
  line-height: 1.5 !important;
  transition: all var(--transition-fast) !important;
  border: none !important;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  text-decoration: none !important;
  position: relative;
  overflow: hidden;
}

.btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left var(--transition-slow);
}

.btn:hover:before {
  left: 100%;
}

.btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md) !important;
}

.btn:active {
  transform: translateY(0);
}

/* Button Variants */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%) !important;
  color: #ffffff !important;
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-hover) 0%, #3730a3 100%) !important;
  color: #ffffff !important;
}

.btn-secondary {
  background: linear-gradient(135deg, var(--gray-500) 0%, var(--gray-600) 100%) !important;
  color: #ffffff !important;
}

.btn-success {
  background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%) !important;
  color: #ffffff !important;
}

.btn-warning {
  background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%) !important;
  color: #ffffff !important;
}

.btn-danger {
  background: linear-gradient(135deg, var(--danger-color) 0%, #dc2626 100%) !important;
  color: #ffffff !important;
}

.btn-info {
  background: linear-gradient(135deg, var(--info-color) 0%, #0891b2 100%) !important;
  color: #ffffff !important;
}

/* Button Sizes */
.btn-xs {
  padding: 0.375rem 0.75rem !important;
  font-size: 0.75rem !important;
  border-radius: var(--radius-sm) !important;
}

.btn-sm {
  padding: 0.5rem 1rem !important;
  font-size: 0.8125rem !important;
}

.btn-lg {
  padding: 0.75rem 1.5rem !important;
  font-size: 1rem !important;
  border-radius: var(--radius-lg) !important;
}

/* ===== ENHANCED FORMS ===== */
.form-control {
  border: 2px solid var(--border-color) !important;
  border-radius: var(--radius-md) !important;
  padding: 0.75rem 1rem !important;
  font-size: 0.875rem !important;
  font-family: var(--font-family-sans) !important;
  transition: all var(--transition-fast) !important;
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
}

.form-control:focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1) !important;
  outline: none !important;
}

.form-control::placeholder {
  color: var(--text-muted) !important;
}

.form-group {
  margin-bottom: var(--spacing-lg) !important;
}

.form-group label {
  font-weight: var(--font-weight-medium) !important;
  color: var(--text-secondary) !important;
  margin-bottom: var(--spacing-sm) !important;
  display: block;
  transition: color var(--transition-normal);
}

/* ===== ENHANCED TABLES ===== */
.table {
  border-collapse: separate !important;
  border-spacing: 0 !important;
  border-radius: var(--radius-lg) !important;
  overflow: hidden !important;
  box-shadow: var(--shadow-sm) !important;
}

.table thead th {
  background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%) !important;
  border-bottom: 2px solid var(--border-color) !important;
  font-weight: var(--font-weight-semibold) !important;
  color: var(--text-secondary) !important;
  padding: var(--spacing-lg) !important;
  font-size: 0.875rem !important;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: color var(--transition-normal);
}

.table tbody tr {
  transition: all var(--transition-fast);
}

.table tbody tr:hover {
  background-color: var(--bg-tertiary) !important;
  transform: scale(1.01);
}

.table tbody td {
  padding: var(--spacing-lg) !important;
  border-bottom: 1px solid var(--border-color) !important;
  color: var(--text-secondary) !important;
  transition: color var(--transition-normal);
}

/* ===== ENHANCED MODALS ===== */
.modal-content {
  border: none !important;
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--shadow-xl) !important;
  overflow: hidden;
}

.modal-header {
  background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-primary) 100%) !important;
  border-bottom: 1px solid var(--border-color) !important;
  padding: var(--spacing-xl) !important;
}

.modal-title {
  font-weight: var(--font-weight-semibold) !important;
  color: var(--text-primary) !important;
  transition: color var(--transition-normal);
}

.modal-body {
  padding: var(--spacing-xl) !important;
  background: var(--bg-primary) !important;
  color: var(--text-primary) !important;
}

.modal-footer {
  background: var(--bg-tertiary) !important;
  border-top: 1px solid var(--border-color) !important;
  padding: var(--spacing-lg) var(--spacing-xl) !important;
}

/* ===== ENHANCED NAVIGATION TABS ===== */
.nav-tabs {
  border-bottom: 2px solid var(--gray-200) !important;
  margin-bottom: var(--spacing-lg) !important;
}

.nav-tabs .nav-link {
  border: none !important;
  border-radius: var(--radius-md) var(--radius-md) 0 0 !important;
  padding: var(--spacing-md) var(--spacing-lg) !important;
  font-weight: var(--font-weight-medium) !important;
  color: var(--gray-600) !important;
  transition: all var(--transition-fast) !important;
  margin-right: var(--spacing-sm) !important;
}

.nav-tabs .nav-link:hover {
  background-color: var(--gray-100) !important;
  color: var(--gray-700) !important;
}

.nav-tabs .nav-link.active {
  background-color: var(--primary-color) !important;
  color: #ffffff !important;
  border-bottom: 2px solid var(--primary-color) !important;
}

/* ===== ENHANCED FOOTER ===== */
.main-footer {
  background: linear-gradient(135deg, #ffffff 0%, var(--gray-50) 100%) !important;
  border-top: 1px solid var(--gray-200) !important;
  color: var(--gray-600) !important;
  padding: var(--spacing-xl) !important;
  margin-top: var(--spacing-2xl) !important;
}

/* ===== UTILITY CLASSES ===== */
.shadow-modern {
  box-shadow: var(--shadow-lg) !important;
}

.rounded-modern {
  border-radius: var(--radius-xl) !important;
}

.text-gradient {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* ===== RESPONSIVE IMPROVEMENTS ===== */
@media (max-width: 768px) {
  .card {
    margin-bottom: var(--spacing-lg) !important;
  }
  
  .card-body {
    padding: var(--spacing-lg) !important;
  }
  
  .btn {
    padding: 0.5rem 1rem !important;
    font-size: 0.8125rem !important;
  }
  
  h1 { font-size: 1.875rem; }
  h2 { font-size: 1.5rem; }
  h3 { font-size: 1.25rem; }
}

/* ===== ANIMATION KEYFRAMES ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp var(--transition-normal) ease-out;
}

.animate-slide-in-right {
  animation: slideInRight var(--transition-normal) ease-out;
}

/* ===== ENHANCED ALERTS & NOTIFICATIONS ===== */
.alert {
  border: none !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--spacing-lg) !important;
  margin-bottom: var(--spacing-lg) !important;
  box-shadow: var(--shadow-sm) !important;
  font-weight: var(--font-weight-medium) !important;
}

.alert-success {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%) !important;
  color: #065f46 !important;
  border-left: 4px solid var(--success-color) !important;
}

.alert-warning {
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%) !important;
  color: #92400e !important;
  border-left: 4px solid var(--warning-color) !important;
}

.alert-danger {
  background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%) !important;
  color: #991b1b !important;
  border-left: 4px solid var(--danger-color) !important;
}

.alert-info {
  background: linear-gradient(135deg, #f0f9ff 0%, #bae6fd 100%) !important;
  color: #0c4a6e !important;
  border-left: 4px solid var(--info-color) !important;
}

/* ===== ENHANCED BADGES ===== */
.badge {
  font-weight: var(--font-weight-medium) !important;
  border-radius: var(--radius-md) !important;
  padding: 0.375rem 0.75rem !important;
  font-size: 0.75rem !important;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge-primary {
  background: var(--primary-color) !important;
  color: #ffffff !important;
}

.badge-success {
  background: var(--success-color) !important;
  color: #ffffff !important;
}

.badge-warning {
  background: var(--warning-color) !important;
  color: #ffffff !important;
}

.badge-danger {
  background: var(--danger-color) !important;
  color: #ffffff !important;
}

/* ===== ENHANCED PROGRESS BARS ===== */
.progress {
  height: 0.75rem !important;
  border-radius: var(--radius-md) !important;
  background-color: var(--gray-200) !important;
  overflow: hidden;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.progress-bar {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%) !important;
  border-radius: var(--radius-md) !important;
  transition: width var(--transition-slow) ease-in-out !important;
  position: relative;
  overflow: hidden;
}

.progress-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 35%, rgba(255,255,255,0.2) 35%, rgba(255,255,255,0.2) 65%, transparent 65%);
  background-size: 20px 20px;
  animation: progress-stripes 1s linear infinite;
}

@keyframes progress-stripes {
  0% { background-position: 0 0; }
  100% { background-position: 20px 0; }
}

/* ===== ENHANCED DROPDOWNS ===== */
.dropdown-menu {
  border: none !important;
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-lg) !important;
  padding: var(--spacing-sm) 0 !important;
  margin-top: var(--spacing-sm) !important;
  background: #ffffff !important;
}

.dropdown-item {
  padding: var(--spacing-md) var(--spacing-lg) !important;
  font-weight: var(--font-weight-normal) !important;
  color: var(--gray-700) !important;
  transition: all var(--transition-fast) !important;
}

.dropdown-item:hover {
  background-color: var(--gray-50) !important;
  color: var(--gray-800) !important;
  transform: translateX(4px);
}

.dropdown-item:active {
  background-color: var(--primary-light) !important;
  color: var(--primary-color) !important;
}

/* ===== ENHANCED PAGINATION ===== */
.pagination {
  margin-bottom: 0 !important;
}

.page-link {
  border: 2px solid var(--gray-200) !important;
  border-radius: var(--radius-md) !important;
  padding: 0.5rem 0.75rem !important;
  margin: 0 var(--spacing-xs) !important;
  color: var(--gray-600) !important;
  font-weight: var(--font-weight-medium) !important;
  transition: all var(--transition-fast) !important;
  text-decoration: none !important;
}

.page-link:hover {
  border-color: var(--primary-color) !important;
  background-color: var(--primary-light) !important;
  color: var(--primary-color) !important;
  transform: translateY(-1px);
}

.page-item.active .page-link {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: #ffffff !important;
  box-shadow: var(--shadow-sm);
}

/* ===== ENHANCED BREADCRUMBS ===== */
.breadcrumb {
  background: transparent !important;
  padding: var(--spacing-md) 0 !important;
  margin-bottom: var(--spacing-lg) !important;
}

.breadcrumb-item {
  font-weight: var(--font-weight-medium) !important;
}

.breadcrumb-item a {
  color: var(--gray-600) !important;
  text-decoration: none !important;
  transition: color var(--transition-fast) !important;
}

.breadcrumb-item a:hover {
  color: var(--primary-color) !important;
}

.breadcrumb-item.active {
  color: var(--gray-800) !important;
}

/* ===== ENHANCED LIST GROUPS ===== */
.list-group {
  border-radius: var(--radius-lg) !important;
  overflow: hidden;
  box-shadow: var(--shadow-sm) !important;
}

.list-group-item {
  border: none !important;
  border-bottom: 1px solid var(--gray-200) !important;
  padding: var(--spacing-lg) !important;
  transition: all var(--transition-fast) !important;
  background-color: #ffffff !important;
}

.list-group-item:hover {
  background-color: var(--gray-50) !important;
  transform: translateX(4px);
}

.list-group-item:last-child {
  border-bottom: none !important;
}

.list-group-item.active {
  background-color: var(--primary-color) !important;
  color: #ffffff !important;
  border-color: var(--primary-color) !important;
}

/* ===== ENHANCED TOOLTIPS ===== */
.tooltip {
  font-family: var(--font-family-sans) !important;
}

.tooltip-inner {
  background-color: var(--gray-800) !important;
  border-radius: var(--radius-md) !important;
  padding: var(--spacing-sm) var(--spacing-md) !important;
  font-size: 0.75rem !important;
  font-weight: var(--font-weight-medium) !important;
  box-shadow: var(--shadow-lg) !important;
}

/* ===== ENHANCED SPINNERS & LOADING ===== */
.spinner-border {
  width: 2rem !important;
  height: 2rem !important;
  border-width: 0.25em !important;
  border-color: var(--primary-color) !important;
  border-right-color: transparent !important;
}

.spinner-border-sm {
  width: 1rem !important;
  height: 1rem !important;
  border-width: 0.125em !important;
}

/* Loading overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

/* ===== ENHANCED ACCORDION ===== */
.accordion .card {
  border: 1px solid var(--gray-200) !important;
  margin-bottom: var(--spacing-sm) !important;
}

.accordion .card-header {
  padding: 0 !important;
  background: transparent !important;
  border-bottom: none !important;
}

.accordion .btn-link {
  width: 100%;
  text-align: left;
  padding: var(--spacing-lg) !important;
  color: var(--gray-700) !important;
  font-weight: var(--font-weight-medium) !important;
  text-decoration: none !important;
  border-radius: 0 !important;
  transition: all var(--transition-fast) !important;
}

.accordion .btn-link:hover {
  background-color: var(--gray-50) !important;
  color: var(--gray-800) !important;
}

.accordion .btn-link:not(.collapsed) {
  background-color: var(--primary-light) !important;
  color: var(--primary-color) !important;
}

/* ===== DARK MODE SPECIFIC ENHANCEMENTS ===== */

/* Dark mode text selections */
[data-theme="dark"] ::selection {
  background-color: var(--primary-color);
  color: #ffffff;
}

[data-theme="dark"] ::-moz-selection {
  background-color: var(--primary-color);
  color: #ffffff;
}

/* Dark mode scrollbar */
[data-theme="dark"] ::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background: var(--border-color);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Dark mode focus states */
[data-theme="dark"] *:focus {
  outline-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--primary-color);
}

/* Dark mode navbar brand */
[data-theme="dark"] .brand-text {
  color: var(--primary-color) !important;
}

/* Dark mode alerts with better contrast */
[data-theme="dark"] .alert-success {
  background: linear-gradient(135deg, #064e3b 0%, #065f46 100%) !important;
  color: #a7f3d0 !important;
  border-left-color: var(--success-color) !important;
}

[data-theme="dark"] .alert-warning {
  background: linear-gradient(135deg, #78350f 0%, #92400e 100%) !important;
  color: #fcd34d !important;
  border-left-color: var(--warning-color) !important;
}

[data-theme="dark"] .alert-danger {
  background: linear-gradient(135deg, #7f1d1d 0%, #991b1b 100%) !important;
  color: #fca5a5 !important;
  border-left-color: var(--danger-color) !important;
}

[data-theme="dark"] .alert-info {
  background: linear-gradient(135deg, #164e63 0%, #0e7490 100%) !important;
  color: #67e8f9 !important;
  border-left-color: var(--info-color) !important;
}

/* Dark mode badges */
[data-theme="dark"] .badge-primary {
  background: var(--primary-color) !important;
  color: #ffffff !important;
}

[data-theme="dark"] .badge-success {
  background: var(--success-color) !important;
  color: #ffffff !important;
}

[data-theme="dark"] .badge-warning {
  background: var(--warning-color) !important;
  color: #000000 !important;
}

[data-theme="dark"] .badge-danger {
  background: var(--danger-color) !important;
  color: #ffffff !important;
}

/* Dark mode dropdown improvements */
[data-theme="dark"] .dropdown-menu {
  background: var(--bg-primary) !important;
  border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .dropdown-item {
  color: var(--text-secondary) !important;
}

[data-theme="dark"] .dropdown-item:hover {
  background-color: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
}

[data-theme="dark"] .dropdown-item:active {
  background-color: var(--primary-light) !important;
  color: var(--primary-color) !important;
}

/* Dark mode pagination */
[data-theme="dark"] .page-link {
  background-color: var(--bg-primary) !important;
  border-color: var(--border-color) !important;
  color: var(--text-secondary) !important;
}

[data-theme="dark"] .page-link:hover {
  background-color: var(--bg-tertiary) !important;
  border-color: var(--primary-color) !important;
  color: var(--primary-color) !important;
}

[data-theme="dark"] .page-item.active .page-link {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: #ffffff !important;
}

/* Dark mode breadcrumbs */
[data-theme="dark"] .breadcrumb-item a {
  color: var(--text-muted) !important;
}

[data-theme="dark"] .breadcrumb-item a:hover {
  color: var(--primary-color) !important;
}

[data-theme="dark"] .breadcrumb-item.active {
  color: var(--text-primary) !important;
}

/* Dark mode list groups */
[data-theme="dark"] .list-group-item {
  background-color: var(--bg-primary) !important;
  border-color: var(--border-color) !important;
  color: var(--text-secondary) !important;
}

[data-theme="dark"] .list-group-item:hover {
  background-color: var(--bg-tertiary) !important;
}

[data-theme="dark"] .list-group-item.active {
  background-color: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: #ffffff !important;
}

/* Dark mode tooltips */
[data-theme="dark"] .tooltip-inner {
  background-color: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
  border: 1px solid var(--border-color);
}

/* Dark mode progress bars */
[data-theme="dark"] .progress {
  background-color: var(--bg-tertiary) !important;
}

/* Dark mode spinners */
[data-theme="dark"] .spinner-border {
  border-color: var(--primary-color) !important;
  border-right-color: transparent !important;
}

/* Dark mode text utilities */
[data-theme="dark"] .text-muted {
  color: var(--text-muted) !important;
}

[data-theme="dark"] .text-primary {
  color: var(--primary-color) !important;
}

[data-theme="dark"] .text-secondary {
  color: var(--text-secondary) !important;
}

/* Dark mode footer */
[data-theme="dark"] .main-footer {
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-tertiary) 100%) !important;
  border-top-color: var(--border-color) !important;
  color: var(--text-muted) !important;
}

/* Dark mode close button for modals */
[data-theme="dark"] .close {
  color: var(--text-primary) !important;
  text-shadow: none !important;
  opacity: 0.8;
}

[data-theme="dark"] .close:hover {
  color: var(--text-primary) !important;
  opacity: 1;
}

/* Dark mode AdminLTE specific overrides */
[data-theme="dark"] .content-wrapper {
  background-color: var(--bg-secondary) !important;
}

[data-theme="dark"] .main-sidebar {
  background-color: var(--bg-primary) !important;
}

/* Dark mode for any remaining white backgrounds */
[data-theme="dark"] .bg-white {
  background-color: var(--bg-primary) !important;
}

[data-theme="dark"] .bg-light {
  background-color: var(--bg-tertiary) !important;
}

/* Dark mode border utilities */
[data-theme="dark"] .border {
  border-color: var(--border-color) !important;
}

[data-theme="dark"] .border-top {
  border-top-color: var(--border-color) !important;
}

[data-theme="dark"] .border-bottom {
  border-bottom-color: var(--border-color) !important;
}

[data-theme="dark"] .border-left {
  border-left-color: var(--border-color) !important;
}

[data-theme="dark"] .border-right {
  border-right-color: var(--border-color) !important;
}
