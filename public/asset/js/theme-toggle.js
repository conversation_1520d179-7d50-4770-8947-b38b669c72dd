/*!
 * Theme Toggle Functionality
 * Handles light/dark mode switching with persistence
 * Author: Custom Enhancement
 * Version: 1.0.0
 */

class ThemeToggle {
    constructor() {
        this.currentTheme = this.getStoredTheme() || this.getPreferredTheme();
        this.init();
    }

    init() {
        // Set initial theme
        this.setTheme(this.currentTheme);
        
        // Create toggle button
        this.createToggleButton();
        
        // Add event listeners
        this.addEventListeners();
        
        // Listen for system theme changes
        this.watchSystemTheme();
    }

    getStoredTheme() {
        return localStorage.getItem('theme');
    }

    getPreferredTheme() {
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            return 'dark';
        }
        return 'light';
    }

    setTheme(theme) {
        this.currentTheme = theme;
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('theme', theme);
        
        // Update toggle button icon
        this.updateToggleIcon();
        
        // Dispatch custom event for other components
        window.dispatchEvent(new CustomEvent('themeChanged', { 
            detail: { theme: theme } 
        }));
    }

    toggleTheme() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.setTheme(newTheme);
        
        // Add a subtle animation to the page
        this.animateThemeChange();
    }

    createToggleButton() {
        // Check if button already exists
        if (document.querySelector('.theme-toggle')) {
            return;
        }

        const toggleButton = document.createElement('button');
        toggleButton.className = 'theme-toggle';
        toggleButton.setAttribute('aria-label', 'Toggle theme');
        toggleButton.setAttribute('title', 'Toggle light/dark theme');
        
        toggleButton.innerHTML = `
            <i class="fas fa-moon" aria-hidden="true"></i>
            <i class="fas fa-sun" aria-hidden="true"></i>
        `;
        
        document.body.appendChild(toggleButton);
        this.toggleButton = toggleButton;
    }

    updateToggleIcon() {
        if (!this.toggleButton) return;
        
        const moonIcon = this.toggleButton.querySelector('.fa-moon');
        const sunIcon = this.toggleButton.querySelector('.fa-sun');
        
        if (this.currentTheme === 'dark') {
            moonIcon.style.display = 'none';
            sunIcon.style.display = 'block';
        } else {
            moonIcon.style.display = 'block';
            sunIcon.style.display = 'none';
        }
    }

    addEventListeners() {
        if (this.toggleButton) {
            this.toggleButton.addEventListener('click', () => {
                this.toggleTheme();
            });

            // Add keyboard support
            this.toggleButton.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.toggleTheme();
                }
            });
        }

        // Listen for storage changes (for multi-tab sync)
        window.addEventListener('storage', (e) => {
            if (e.key === 'theme' && e.newValue !== this.currentTheme) {
                this.setTheme(e.newValue);
            }
        });
    }

    watchSystemTheme() {
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            
            mediaQuery.addEventListener('change', (e) => {
                // Only auto-switch if user hasn't manually set a preference
                if (!localStorage.getItem('theme')) {
                    this.setTheme(e.matches ? 'dark' : 'light');
                }
            });
        }
    }

    animateThemeChange() {
        // Add a subtle fade animation during theme change
        document.body.style.transition = 'background-color 0.3s ease, color 0.3s ease';
        
        setTimeout(() => {
            document.body.style.transition = '';
        }, 300);
    }

    // Public method to manually set theme
    setThemeManually(theme) {
        if (theme === 'light' || theme === 'dark') {
            this.setTheme(theme);
        }
    }

    // Public method to get current theme
    getCurrentTheme() {
        return this.currentTheme;
    }

    // Public method to reset to system preference
    resetToSystemTheme() {
        localStorage.removeItem('theme');
        this.setTheme(this.getPreferredTheme());
    }
}

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize theme toggle
    window.themeToggle = new ThemeToggle();
    
    // Add some additional enhancements
    enhanceThemeExperience();
});

function enhanceThemeExperience() {
    // Add smooth transitions to all elements when theme changes
    const style = document.createElement('style');
    style.textContent = `
        *, *::before, *::after {
            transition: background-color 0.25s ease, 
                       border-color 0.25s ease, 
                       color 0.25s ease, 
                       box-shadow 0.25s ease !important;
        }
        
        /* Disable transitions during page load */
        .preload * {
            transition: none !important;
        }
    `;
    document.head.appendChild(style);
    
    // Remove preload class after a short delay
    document.body.classList.add('preload');
    setTimeout(() => {
        document.body.classList.remove('preload');
    }, 100);
    
    // Add theme change event listener for custom components
    window.addEventListener('themeChanged', function(e) {
        console.log('Theme changed to:', e.detail.theme);
        
        // Update any third-party components that need manual theme updates
        updateThirdPartyComponents(e.detail.theme);
    });
}

function updateThirdPartyComponents(theme) {
    // Update SweetAlert2 theme if present
    if (window.Swal) {
        const isDark = theme === 'dark';
        // You can customize SweetAlert2 styling here based on theme
    }
    
    // Update DataTables theme if present
    if (window.jQuery && window.jQuery.fn.DataTable) {
        // DataTables theme updates can be added here
    }
    
    // Update any other third-party components
    // Add your custom component updates here
}

// Utility functions for external use
window.ThemeUtils = {
    getCurrentTheme: () => window.themeToggle?.getCurrentTheme() || 'light',
    setTheme: (theme) => window.themeToggle?.setThemeManually(theme),
    toggleTheme: () => window.themeToggle?.toggleTheme(),
    resetToSystem: () => window.themeToggle?.resetToSystemTheme(),
    
    // Check if dark mode is active
    isDarkMode: () => window.themeToggle?.getCurrentTheme() === 'dark',
    
    // Get system preference
    getSystemPreference: () => {
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            return 'dark';
        }
        return 'light';
    }
};

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThemeToggle;
}
