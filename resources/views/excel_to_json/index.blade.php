@extends('layout.base', ['title' => 'Excel to JSON Converter'])

@section('body')
    <div class="container pb-5 animate-fade-in-up">
        <!-- Header Section -->
        <div class="text-center mb-5">
            <h1 class="text-gradient mb-3">
                <i class="fas fa-file-excel text-success me-3"></i>
                Excel to JSON Converter
            </h1>
            <p class="lead text-muted">Transform your Excel files into JSON format with ease</p>
        </div>

        <!-- Upload Section -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow-modern mb-5" id="upload-card">
                    <div class="card-header bg-gradient-primary text-white">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-cloud-upload-alt me-2"></i>
                            Upload Your Excel File
                        </h3>
                    </div>
                    <div class="card-body">
                        <!-- Drag & Drop Zone -->
                        <div class="upload-zone" id="upload-zone">
                            <div class="upload-zone-content">
                                <i class="fas fa-cloud-upload-alt upload-icon"></i>
                                <h4>Drag & Drop your Excel file here</h4>
                                <p class="text-muted">or click to browse files</p>
                                <div class="supported-formats">
                                    <span class="badge badge-primary me-2">.xlsx</span>
                                    <span class="badge badge-primary me-2">.xls</span>
                                    <span class="badge badge-primary">.csv</span>
                                </div>
                            </div>
                            <input type="file" class="file-input" id="file_upload"
                                   accept=".csv,.xlsx,.xls,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel">
                        </div>

                        <!-- File Info Display -->
                        <div class="file-info" id="file-info" style="display: none;">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-file-excel text-success me-3 fs-2"></i>
                                <div class="flex-grow-1">
                                    <h5 class="mb-1" id="file-name"></h5>
                                    <small class="text-muted" id="file-size"></small>
                                </div>
                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="clearFile()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Progress Bar -->
                        <div class="progress-container" id="progress-container" style="display: none;">
                            <div class="progress mb-3">
                                <div class="progress-bar progress-bar-striped progress-bar-animated"
                                     id="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="text-center">
                                <small class="text-muted" id="progress-text">Processing...</small>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer text-center">
                        <button type="button" class="btn btn-primary btn-lg" id="convert-btn" onclick="upload()" disabled>
                            <i class="fas fa-magic me-2"></i>
                            Convert to JSON
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Section -->
        <div class="row" id="results-section" style="display: none;">
            <div class="col-12">
                <div class="card shadow-modern">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h3 class="card-title mb-0">
                            <i class="fas fa-code text-info me-2"></i>
                            JSON Output
                        </h3>
                        <div class="btn-group">
                            <button type="button" class="btn btn-outline-success btn-sm" onclick="downloadJSON()">
                                <i class="fas fa-download me-1"></i>
                                Download
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="copyToClipboard()">
                                <i class="fas fa-copy me-1"></i>
                                Copy
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-sm" onclick="formatJSON()">
                                <i class="fas fa-indent me-1"></i>
                                Format
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="json-container">
                            <pre><code class="language-json" id="json-result"></code></pre>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="row text-center">
                            <div class="col-md-4">
                                <small class="text-muted">
                                    <i class="fas fa-table me-1"></i>
                                    Sheets: <span id="sheet-count">0</span>
                                </small>
                            </div>
                            <div class="col-md-4">
                                <small class="text-muted">
                                    <i class="fas fa-list me-1"></i>
                                    Records: <span id="record-count">0</span>
                                </small>
                            </div>
                            <div class="col-md-4">
                                <small class="text-muted">
                                    <i class="fas fa-weight me-1"></i>
                                    Size: <span id="json-size">0 KB</span>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@section('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-json.min.js"></script>
    <script>
        // Global variables
        let currentFile = null;
        let jsonData = null;
        let isProcessing = false;

        // Initialize when DOM is ready
        $(document).ready(function() {
            initializeUploadZone();
            initializeTooltips();
        });

        function initializeUploadZone() {
            const uploadZone = document.getElementById('upload-zone');
            const fileInput = document.getElementById('file_upload');

            // Drag and drop events
            uploadZone.addEventListener('dragover', handleDragOver);
            uploadZone.addEventListener('dragleave', handleDragLeave);
            uploadZone.addEventListener('drop', handleDrop);
            uploadZone.addEventListener('click', () => fileInput.click());

            // File input change event
            fileInput.addEventListener('change', handleFileSelect);
        }

        function initializeTooltips() {
            // Initialize Bootstrap tooltips if available
            if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
                var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });
            }
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.stopPropagation();
            e.currentTarget.classList.add('drag-over');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            e.stopPropagation();
            e.currentTarget.classList.remove('drag-over');
        }

        function handleDrop(e) {
            e.preventDefault();
            e.stopPropagation();
            e.currentTarget.classList.remove('drag-over');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileSelect({ target: { files: files } });
            }
        }

        function handleFileSelect(e) {
            const files = e.target.files;
            if (files.length === 0) return;

            const file = files[0];
            if (validateFile(file)) {
                currentFile = file;
                displayFileInfo(file);
                enableConvertButton();
            }
        }

        function validateFile(file) {
            const validTypes = [
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-excel',
                'text/csv'
            ];

            const validExtensions = ['.xlsx', '.xls', '.csv'];
            const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

            if (!validTypes.includes(file.type) && !validExtensions.includes(fileExtension)) {
                showError('Please select a valid Excel file (.xlsx, .xls) or CSV file.');
                return false;
            }

            if (file.size > 50 * 1024 * 1024) { // 50MB limit
                showError('File size must be less than 50MB.');
                return false;
            }

            return true;
        }

        function displayFileInfo(file) {
            document.getElementById('file-name').textContent = file.name;
            document.getElementById('file-size').textContent = formatFileSize(file.size);
            document.getElementById('file-info').style.display = 'block';
            document.getElementById('upload-zone').style.display = 'none';
        }

        function clearFile() {
            currentFile = null;
            document.getElementById('file_upload').value = '';
            document.getElementById('file-info').style.display = 'none';
            document.getElementById('upload-zone').style.display = 'block';
            document.getElementById('convert-btn').disabled = true;
            hideResults();
        }

        function enableConvertButton() {
            document.getElementById('convert-btn').disabled = false;
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function upload() {
            if (!currentFile) {
                showError("Please select a file first.");
                return;
            }

            if (isProcessing) return;

            isProcessing = true;
            showProgress();
            disableConvertButton();

            // Simulate progress for better UX
            simulateProgress();

            // Process file
            setTimeout(() => {
                excelFileToJSON(currentFile);
            }, 500);
        }

        function showProgress() {
            document.getElementById('progress-container').style.display = 'block';
            updateProgress(0, 'Initializing...');
        }

        function hideProgress() {
            document.getElementById('progress-container').style.display = 'none';
        }

        function updateProgress(percent, text) {
            const progressBar = document.getElementById('progress-bar');
            const progressText = document.getElementById('progress-text');

            progressBar.style.width = percent + '%';
            progressBar.setAttribute('aria-valuenow', percent);
            progressText.textContent = text;
        }

        function simulateProgress() {
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 90) {
                    clearInterval(interval);
                    return;
                }
                updateProgress(progress, 'Processing file...');
            }, 200);
        }

        function disableConvertButton() {
            const btn = document.getElementById('convert-btn');
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Converting...';
        }

        function enableConvertButton() {
            const btn = document.getElementById('convert-btn');
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-magic me-2"></i>Convert to JSON';
        }

        function excelFileToJSON(file) {
            try {
                updateProgress(30, 'Reading file...');

                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        updateProgress(50, 'Parsing Excel data...');

                        const data = e.target.result;
                        const workbook = XLSX.read(data, { type: 'binary' });

                        updateProgress(70, 'Converting to JSON...');

                        const result = {};
                        let totalRecords = 0;

                        workbook.SheetNames.forEach(function(sheetName) {
                            const worksheet = workbook.Sheets[sheetName];
                            const jsonData = XLSX.utils.sheet_to_json(worksheet, {
                                header: 1,
                                defval: null,
                                blankrows: false
                            });

                            if (jsonData.length > 0) {
                                // Convert array of arrays to array of objects
                                const headers = jsonData[0];
                                const rows = jsonData.slice(1);

                                const sheetData = rows.map(row => {
                                    const obj = {};
                                    headers.forEach((header, index) => {
                                        obj[header] = row[index] || null;
                                    });
                                    return obj;
                                });

                                result[sheetName] = sheetData;
                                totalRecords += sheetData.length;
                            }
                        });

                        updateProgress(90, 'Finalizing...');

                        jsonData = result;
                        displayResults(result, workbook.SheetNames.length, totalRecords);

                        updateProgress(100, 'Complete!');

                        setTimeout(() => {
                            hideProgress();
                            enableConvertButton();
                            isProcessing = false;
                        }, 500);

                    } catch (error) {
                        console.error('Error processing file:', error);
                        showError('Error processing the Excel file. Please check the file format.');
                        hideProgress();
                        enableConvertButton();
                        isProcessing = false;
                    }
                };

                reader.onerror = function() {
                    showError('Error reading the file.');
                    hideProgress();
                    enableConvertButton();
                    isProcessing = false;
                };

                reader.readAsBinaryString(file);

            } catch (error) {
                console.error('Error:', error);
                showError('An unexpected error occurred.');
                hideProgress();
                enableConvertButton();
                isProcessing = false;
            }
        }

        function displayResults(data, sheetCount, recordCount) {
            const jsonString = JSON.stringify(data, null, 2);
            const resultElement = document.getElementById('json-result');

            resultElement.textContent = jsonString;

            // Update statistics
            document.getElementById('sheet-count').textContent = sheetCount;
            document.getElementById('record-count').textContent = recordCount;
            document.getElementById('json-size').textContent = formatFileSize(new Blob([jsonString]).size);

            // Show results section
            document.getElementById('results-section').style.display = 'block';

            // Scroll to results
            document.getElementById('results-section').scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });

            // Apply syntax highlighting if Prism is available
            if (typeof Prism !== 'undefined') {
                Prism.highlightElement(resultElement);
            }
        }

        function hideResults() {
            document.getElementById('results-section').style.display = 'none';
            jsonData = null;
        }

        function downloadJSON() {
            if (!jsonData) {
                showError('No JSON data to download.');
                return;
            }

            const jsonString = JSON.stringify(jsonData, null, 2);
            const blob = new Blob([jsonString], { type: 'application/json' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = (currentFile ? currentFile.name.replace(/\.[^/.]+$/, '') : 'converted') + '.json';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showSuccess('JSON file downloaded successfully!');
        }

        function copyToClipboard() {
            if (!jsonData) {
                showError('No JSON data to copy.');
                return;
            }

            const jsonString = JSON.stringify(jsonData, null, 2);

            if (navigator.clipboard) {
                navigator.clipboard.writeText(jsonString).then(() => {
                    showSuccess('JSON copied to clipboard!');
                }).catch(() => {
                    fallbackCopyToClipboard(jsonString);
                });
            } else {
                fallbackCopyToClipboard(jsonString);
            }
        }

        function fallbackCopyToClipboard(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                document.execCommand('copy');
                showSuccess('JSON copied to clipboard!');
            } catch (err) {
                showError('Failed to copy to clipboard.');
            }

            document.body.removeChild(textArea);
        }

        function formatJSON() {
            if (!jsonData) {
                showError('No JSON data to format.');
                return;
            }

            // Toggle between formatted and minified
            const resultElement = document.getElementById('json-result');
            const currentText = resultElement.textContent;

            try {
                const parsed = JSON.parse(currentText);
                const isFormatted = currentText.includes('\n');
                const newText = isFormatted ?
                    JSON.stringify(parsed) :
                    JSON.stringify(parsed, null, 2);

                resultElement.textContent = newText;

                // Update size
                document.getElementById('json-size').textContent = formatFileSize(new Blob([newText]).size);

                // Re-apply syntax highlighting
                if (typeof Prism !== 'undefined') {
                    Prism.highlightElement(resultElement);
                }

                showSuccess(isFormatted ? 'JSON minified!' : 'JSON formatted!');
            } catch (error) {
                showError('Error formatting JSON.');
            }
        }

        function showError(message) {
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: message,
                    confirmButtonColor: '#ef4444'
                });
            } else {
                alert('Error: ' + message);
            }
        }

        function showSuccess(message) {
            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: message,
                    timer: 2000,
                    showConfirmButton: false,
                    confirmButtonColor: '#10b981'
                });
            } else {
                alert(message);
            }
        }
    </script>
@endsection
@section('css')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css">
    <style>
        /* Custom styles for Excel to JSON converter */
        .text-gradient {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--success-color) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: var(--font-weight-bold);
        }

        .bg-gradient-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%) !important;
        }

        /* Upload Zone Styles */
        .upload-zone {
            border: 3px dashed var(--border-color);
            border-radius: var(--radius-xl);
            padding: 3rem 2rem;
            text-align: center;
            transition: all var(--transition-normal);
            cursor: pointer;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-tertiary) 100%);
            position: relative;
            overflow: hidden;
        }

        .upload-zone:hover {
            border-color: var(--primary-color);
            background: linear-gradient(135deg, var(--primary-light) 0%, var(--bg-tertiary) 100%);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .upload-zone.drag-over {
            border-color: var(--success-color);
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, var(--bg-tertiary) 100%);
            transform: scale(1.02);
        }

        .upload-zone-content h4 {
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
            font-weight: var(--font-weight-semibold);
        }

        .upload-zone-content p {
            color: var(--text-muted);
            margin-bottom: var(--spacing-lg);
        }

        .upload-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: var(--spacing-lg);
            transition: all var(--transition-normal);
        }

        .upload-zone:hover .upload-icon {
            transform: scale(1.1);
            color: var(--primary-hover);
        }

        .file-input {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }

        .supported-formats {
            margin-top: var(--spacing-lg);
        }

        .supported-formats .badge {
            font-size: 0.75rem;
            padding: 0.375rem 0.75rem;
        }

        /* File Info Styles */
        .file-info {
            background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-primary) 100%);
            border: 2px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            margin-top: var(--spacing-lg);
            animation: slideInRight var(--transition-normal) ease-out;
        }

        .file-info h5 {
            color: var(--text-primary);
            font-weight: var(--font-weight-semibold);
        }

        /* Progress Container */
        .progress-container {
            margin-top: var(--spacing-lg);
            animation: fadeInUp var(--transition-normal) ease-out;
        }

        .progress {
            height: 0.75rem;
            border-radius: var(--radius-md);
            background-color: var(--gray-200);
            overflow: hidden;
            box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .progress-bar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--success-color) 100%);
            border-radius: var(--radius-md);
            transition: width var(--transition-slow) ease-in-out;
            position: relative;
            overflow: hidden;
        }

        .progress-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 35%, rgba(255,255,255,0.2) 35%, rgba(255,255,255,0.2) 65%, transparent 65%);
            background-size: 20px 20px;
            animation: progress-stripes 1s linear infinite;
        }

        @keyframes progress-stripes {
            0% { background-position: 0 0; }
            100% { background-position: 20px 0; }
        }

        /* JSON Container Styles */
        .json-container {
            max-height: 600px;
            overflow-y: auto;
            background: var(--bg-secondary);
            border-radius: var(--radius-md);
        }

        .json-container pre {
            margin: 0;
            padding: var(--spacing-lg);
            background: transparent;
            border: none;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.875rem;
            line-height: 1.6;
            color: var(--text-primary);
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .json-container code {
            background: transparent;
            color: inherit;
            padding: 0;
            font-size: inherit;
        }

        /* Results Section Animation */
        #results-section {
            animation: fadeInUp var(--transition-slow) ease-out;
        }

        /* Button Enhancements */
        .btn-group .btn {
            border-radius: var(--radius-md);
            margin-left: 0.25rem;
        }

        .btn-group .btn:first-child {
            margin-left: 0;
        }

        /* Responsive Improvements */
        @media (max-width: 768px) {
            .upload-zone {
                padding: 2rem 1rem;
            }

            .upload-icon {
                font-size: 2rem;
            }

            .btn-group {
                flex-direction: column;
                width: 100%;
            }

            .btn-group .btn {
                margin: 0.25rem 0 0 0;
                width: 100%;
            }

            .json-container {
                max-height: 400px;
            }
        }

        /* Dark Mode Specific Styles */
        [data-theme="dark"] .upload-zone {
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-tertiary) 100%);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .upload-zone:hover {
            background: linear-gradient(135deg, var(--primary-light) 0%, var(--bg-tertiary) 100%);
        }

        [data-theme="dark"] .file-info {
            background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-primary) 100%);
            border-color: var(--border-color);
        }

        [data-theme="dark"] .json-container {
            background: var(--bg-secondary);
        }

        [data-theme="dark"] .progress {
            background-color: var(--bg-tertiary);
        }

        /* Prism.js Dark Mode Override */
        [data-theme="dark"] .json-container pre[class*="language-"] {
            background: var(--bg-secondary) !important;
            color: var(--text-primary) !important;
        }

        [data-theme="dark"] .token.string {
            color: #a7f3d0 !important;
        }

        [data-theme="dark"] .token.number {
            color: #fbbf24 !important;
        }

        [data-theme="dark"] .token.boolean {
            color: #f87171 !important;
        }

        [data-theme="dark"] .token.null {
            color: #94a3b8 !important;
        }

        [data-theme="dark"] .token.property {
            color: #60a5fa !important;
        }

        [data-theme="dark"] .token.punctuation {
            color: var(--text-secondary) !important;
        }
    </style>
@endsection
