@extends('layout/base')

@section('body')
    <div class="container pb-5">
        <div class="row">
            <div class="col-12">
                <h1 class="text-gradient text-center mb-4">Modern UI Enhancements Demo</h1>
                <p class="text-center text-muted mb-3">Showcasing the enhanced AdminLTE components with modern styling and dark mode support</p>
                <div class="text-center mb-5">
                    <div class="alert alert-info d-inline-block">
                        <i class="fas fa-lightbulb mr-2"></i>
                        <strong>Try the dark mode!</strong> Click the theme toggle button in the top-right corner to switch between light and dark themes.
                    </div>
                </div>
            </div>
        </div>

        <!-- Theme Controls Demo -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Dark Mode Controls</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h5>Theme Toggle</h5>
                                <p>Use the floating toggle button in the top-right corner, or use these manual controls:</p>
                                <button type="button" class="btn btn-primary mr-2 mb-2" onclick="ThemeUtils.setTheme('light')">
                                    <i class="fas fa-sun mr-1"></i> Light Mode
                                </button>
                                <button type="button" class="btn btn-secondary mr-2 mb-2" onclick="ThemeUtils.setTheme('dark')">
                                    <i class="fas fa-moon mr-1"></i> Dark Mode
                                </button>
                                <button type="button" class="btn btn-info mr-2 mb-2" onclick="ThemeUtils.resetToSystem()">
                                    <i class="fas fa-desktop mr-1"></i> System Default
                                </button>
                            </div>
                            <div class="col-md-6">
                                <h5>Current Theme Status</h5>
                                <div class="alert alert-success">
                                    <strong>Current Theme:</strong> <span id="current-theme">Loading...</span><br>
                                    <strong>System Preference:</strong> <span id="system-theme">Loading...</span><br>
                                    <strong>Stored Preference:</strong> <span id="stored-theme">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cards Demo -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card animate-fade-in-up">
                    <div class="card-header">
                        <h3 class="card-title">Enhanced Card</h3>
                    </div>
                    <div class="card-body">
                        <p>This card demonstrates the modern enhancements including rounded corners, subtle shadows, and smooth hover effects.</p>
                        <div class="progress mb-3">
                            <div class="progress-bar" role="progressbar" style="width: 75%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <span class="badge badge-primary">New Feature</span>
                        <span class="badge badge-success">Enhanced</span>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card card-primary card-outline animate-slide-in-right">
                    <div class="card-header">
                        <h3 class="card-title">Primary Outlined Card</h3>
                    </div>
                    <div class="card-body">
                        <p>Cards with outline styling maintain the modern aesthetic while providing visual hierarchy.</p>
                        <div class="alert alert-info">
                            <strong>Info:</strong> This is an enhanced alert with modern styling.
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Buttons Demo -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Enhanced Buttons</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h5>Button Variants</h5>
                                <button type="button" class="btn btn-primary mr-2 mb-2">Primary</button>
                                <button type="button" class="btn btn-secondary mr-2 mb-2">Secondary</button>
                                <button type="button" class="btn btn-success mr-2 mb-2">Success</button>
                                <button type="button" class="btn btn-warning mr-2 mb-2">Warning</button>
                                <button type="button" class="btn btn-danger mr-2 mb-2">Danger</button>
                                <button type="button" class="btn btn-info mr-2 mb-2">Info</button>
                            </div>
                            <div class="col-md-6">
                                <h5>Button Sizes</h5>
                                <button type="button" class="btn btn-primary btn-lg mr-2 mb-2">Large</button>
                                <button type="button" class="btn btn-primary mr-2 mb-2">Default</button>
                                <button type="button" class="btn btn-primary btn-sm mr-2 mb-2">Small</button>
                                <button type="button" class="btn btn-primary btn-xs mr-2 mb-2">Extra Small</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Forms Demo -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Enhanced Forms</h3>
                    </div>
                    <div class="card-body">
                        <form>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="firstName">First Name</label>
                                        <input type="text" class="form-control" id="firstName" placeholder="Enter first name">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="lastName">Last Name</label>
                                        <input type="text" class="form-control" id="lastName" placeholder="Enter last name">
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="email">Email Address</label>
                                <input type="email" class="form-control" id="email" placeholder="Enter email">
                            </div>
                            <div class="form-group">
                                <label for="message">Message</label>
                                <textarea class="form-control" id="message" rows="4" placeholder="Enter your message"></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">Submit Form</button>
                            <button type="reset" class="btn btn-secondary ml-2">Reset</button>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Alerts & Notifications</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <strong>Success!</strong> Operation completed successfully.
                        </div>
                        <div class="alert alert-warning">
                            <strong>Warning!</strong> Please check your input.
                        </div>
                        <div class="alert alert-danger">
                            <strong>Error!</strong> Something went wrong.
                        </div>
                        <div class="alert alert-info">
                            <strong>Info:</strong> Additional information available.
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Table Demo -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Enhanced Table</h3>
                    </div>
                    <div class="card-body">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Email</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>John Doe</td>
                                    <td><EMAIL></td>
                                    <td><span class="badge badge-success">Active</span></td>
                                    <td>
                                        <button class="btn btn-primary btn-xs">Edit</button>
                                        <button class="btn btn-danger btn-xs ml-1">Delete</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>Jane Smith</td>
                                    <td><EMAIL></td>
                                    <td><span class="badge badge-warning">Pending</span></td>
                                    <td>
                                        <button class="btn btn-primary btn-xs">Edit</button>
                                        <button class="btn btn-danger btn-xs ml-1">Delete</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td>Bob Johnson</td>
                                    <td><EMAIL></td>
                                    <td><span class="badge badge-danger">Inactive</span></td>
                                    <td>
                                        <button class="btn btn-primary btn-xs">Edit</button>
                                        <button class="btn btn-danger btn-xs ml-1">Delete</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabs Demo -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card card-primary card-outline card-outline-tabs">
                    <div class="card-header p-0 border-bottom-0">
                        <ul class="nav nav-tabs" id="custom-tabs-demo" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="tab1-tab" data-toggle="pill" href="#tab1" role="tab" aria-controls="tab1" aria-selected="true">Tab 1</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="tab2-tab" data-toggle="pill" href="#tab2" role="tab" aria-controls="tab2" aria-selected="false">Tab 2</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="tab3-tab" data-toggle="pill" href="#tab3" role="tab" aria-controls="tab3" aria-selected="false">Tab 3</a>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content" id="custom-tabs-demo-content">
                            <div class="tab-pane fade show active" id="tab1" role="tabpanel" aria-labelledby="tab1-tab">
                                <h5>Enhanced Navigation Tabs</h5>
                                <p>The navigation tabs have been enhanced with modern styling, smooth transitions, and better visual hierarchy.</p>
                            </div>
                            <div class="tab-pane fade" id="tab2" role="tabpanel" aria-labelledby="tab2-tab">
                                <h5>Smooth Transitions</h5>
                                <p>Notice the smooth fade transitions when switching between tabs. This provides a more polished user experience.</p>
                            </div>
                            <div class="tab-pane fade" id="tab3" role="tabpanel" aria-labelledby="tab3-tab">
                                <h5>Consistent Styling</h5>
                                <p>All components maintain consistent spacing, colors, and typography for a cohesive design system.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- List Group Demo -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Enhanced List Group</h3>
                    </div>
                    <div class="card-body p-0">
                        <div class="list-group list-group-flush">
                            <div class="list-group-item">
                                <strong>Dashboard</strong><br>
                                <small class="text-muted">Main overview page</small>
                            </div>
                            <div class="list-group-item active">
                                <strong>UI Demo</strong><br>
                                <small class="text-muted">Component showcase</small>
                            </div>
                            <div class="list-group-item">
                                <strong>Settings</strong><br>
                                <small class="text-muted">Application configuration</small>
                            </div>
                            <div class="list-group-item">
                                <strong>Profile</strong><br>
                                <small class="text-muted">User account settings</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Loading States</h3>
                    </div>
                    <div class="card-body text-center">
                        <div class="spinner-border text-primary mb-3" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <div class="spinner-border spinner-border-sm text-secondary mb-3 ml-2" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                        <br>
                        <button class="btn btn-primary" type="button" disabled>
                            <span class="spinner-border spinner-border-sm mr-2" role="status" aria-hidden="true"></span>
                            Loading...
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('css')
    <style>
        /* Demo-specific styles */
        .demo-section {
            margin-bottom: 2rem;
        }
    </style>
@endsection

@section('js')
    <script>
        // Demo JavaScript for interactions
        $(document).ready(function() {
            // Add some interactive demo functionality
            $('.btn').on('click', function(e) {
                if ($(this).text().includes('Submit') || $(this).text().includes('Delete')) {
                    e.preventDefault();
                    alert('This is a demo - no actual action performed');
                }
            });

            // Update theme status display
            updateThemeStatus();

            // Listen for theme changes
            window.addEventListener('themeChanged', function(e) {
                updateThemeStatus();
            });
        });

        function updateThemeStatus() {
            // Wait a bit for ThemeUtils to be available
            setTimeout(function() {
                if (window.ThemeUtils) {
                    document.getElementById('current-theme').textContent = ThemeUtils.getCurrentTheme();
                    document.getElementById('system-theme').textContent = ThemeUtils.getSystemPreference();
                    document.getElementById('stored-theme').textContent = localStorage.getItem('theme') || 'None (using system)';
                }
            }, 100);
        }

        // Add some theme-aware demo functionality
        function demonstrateThemeFeatures() {
            const isDark = ThemeUtils.isDarkMode();
            const message = isDark
                ? 'Dark mode is active! Notice the enhanced contrast and reduced eye strain.'
                : 'Light mode is active! Clean and bright interface for daytime use.';

            // Use SweetAlert2 if available, otherwise use regular alert
            if (window.Swal) {
                Swal.fire({
                    title: 'Theme Demo',
                    text: message,
                    icon: 'info',
                    confirmButtonText: 'Got it!'
                });
            } else {
                alert(message);
            }
        }
    </script>
@endsection
